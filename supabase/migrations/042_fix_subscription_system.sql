-- Fix subscription system issues: RLS policies and missing RPC functions
-- This addresses 404 and 406 errors in subscription functionality

-- First, clean up conflicting RLS policies on subscriptions table
DROP POLICY IF EXISTS "Content creators can view their subscribers" ON subscriptions;
DROP POLICY IF EXISTS "Writers can view their subscriptions" ON subscriptions;

-- Create a single, clear policy for writers to view their subscriptions
CREATE POLICY "Writers can view their subscriber data" ON subscriptions
    FOR SELECT USING (auth.uid() = writer_id);

-- Ensure readers can manage their own subscriptions
-- (This policy should already exist but let's make sure it's correct)
DROP POLICY IF EXISTS "Readers can manage their subscriptions" ON subscriptions;
CREATE POLICY "Readers can manage their subscriptions" ON subscriptions
    FOR ALL USING (auth.uid() = reader_id);

-- Create missing RPC functions for post navigation
CREATE OR REPLACE FUNCTION get_next_entry(
  p_writer_id uuid,
  p_current_entry_id uuid
)
RETURNS TABLE(
  id uuid,
  title text,
  created_at timestamp with time zone
) 
SET search_path = ''
AS $$
BEGIN
  RETURN QUERY
  SELECT 
    de.id,
    de.title,
    de.created_at
  FROM public.diary_entries de
  WHERE de.user_id = p_writer_id
    AND de.is_hidden = false
    AND de.created_at > (
      SELECT created_at 
      FROM public.diary_entries 
      WHERE id = p_current_entry_id
    )
  ORDER BY de.created_at ASC
  LIMIT 1;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

CREATE OR REPLACE FUNCTION get_previous_entry(
  p_writer_id uuid,
  p_current_entry_id uuid
)
RETURNS TABLE(
  id uuid,
  title text,
  created_at timestamp with time zone
) 
SET search_path = ''
AS $$
BEGIN
  RETURN QUERY
  SELECT 
    de.id,
    de.title,
    de.created_at
  FROM public.diary_entries de
  WHERE de.user_id = p_writer_id
    AND de.is_hidden = false
    AND de.created_at < (
      SELECT created_at 
      FROM public.diary_entries 
      WHERE id = p_current_entry_id
    )
  ORDER BY de.created_at DESC
  LIMIT 1;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create reading_positions table if it doesn't exist
CREATE TABLE IF NOT EXISTS reading_positions (
  id uuid DEFAULT gen_random_uuid() PRIMARY KEY,
  user_id uuid NOT NULL REFERENCES users(id) ON DELETE CASCADE,
  writer_id uuid NOT NULL REFERENCES users(id) ON DELETE CASCADE,
  last_read_entry_id uuid REFERENCES diary_entries(id) ON DELETE SET NULL,
  last_read_at timestamp with time zone DEFAULT now(),
  created_at timestamp with time zone DEFAULT now(),
  updated_at timestamp with time zone DEFAULT now(),
  
  CONSTRAINT unique_user_writer_reading_position UNIQUE (user_id, writer_id)
);

-- Create RLS policies for reading_positions
ALTER TABLE reading_positions ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Users can manage their own reading positions" ON reading_positions
    FOR ALL USING (auth.uid() = user_id);

-- Create the missing update_reading_position function
CREATE OR REPLACE FUNCTION update_reading_position(
  p_user_id uuid,
  p_writer_id uuid,
  p_entry_id uuid
)
RETURNS void 
SET search_path = ''
AS $$
BEGIN
  INSERT INTO public.reading_positions (user_id, writer_id, last_read_entry_id, last_read_at)
  VALUES (p_user_id, p_writer_id, p_entry_id, now())
  ON CONFLICT (user_id, writer_id)
  DO UPDATE SET
    last_read_entry_id = p_entry_id,
    last_read_at = now(),
    updated_at = now();
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Grant permissions for the new functions
GRANT EXECUTE ON FUNCTION get_next_entry(uuid, uuid) TO authenticated;
GRANT EXECUTE ON FUNCTION get_previous_entry(uuid, uuid) TO authenticated;
GRANT EXECUTE ON FUNCTION update_reading_position(uuid, uuid, uuid) TO authenticated;

-- Add indexes for performance
CREATE INDEX IF NOT EXISTS idx_reading_positions_user_id ON reading_positions(user_id);
CREATE INDEX IF NOT EXISTS idx_reading_positions_writer_id ON reading_positions(writer_id);
CREATE INDEX IF NOT EXISTS idx_reading_positions_last_read ON reading_positions(last_read_at);
